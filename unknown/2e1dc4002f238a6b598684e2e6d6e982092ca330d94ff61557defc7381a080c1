import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { Controller } from 'react-hook-form';
import { Colors } from '../../constants/colors';
import { mockUsers } from '../../store/mockData/users';
import { User, UserRole } from '../../types/User';

interface StudentSplitPickerProps {
  control: any;
  name: string;
  error?: string;
}

export default function StudentSplitPicker({
  control,
  name,
  error,
}: StudentSplitPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Filter to get only students
  const students = mockUsers.filter(user => user.role === UserRole.STUDENT);

  const renderStudentItem = (
    student: User, 
    selectedStudents: string[], 
    onToggleStudent: (studentId: string) => void
  ) => {
    const isSelected = selectedStudents.includes(student.id);
    
    return (
      <TouchableOpacity
        key={student.id}
        style={styles.dropdownItem}
        onPress={() => onToggleStudent(student.id)}
      >
        <View style={styles.checkboxContainer}>
          <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
            {isSelected && <Text style={styles.checkmark}>✓</Text>}
          </View>
          <View style={styles.studentInfo}>
            <Text style={styles.studentName}>{student.name}</Text>
            <Text style={styles.studentEmail}>{student.email}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const getDisplayText = (selectedStudents: string[]) => {
    if (selectedStudents.length === 0) {
      return 'Select students to split with';
    }

    const selectedNames = selectedStudents
      .map(id => students.find(s => s.id === id)?.name)
      .filter(Boolean);

    if (selectedNames.length === 0) {
      return 'Selected students';
    }

    // For multiple students, show each on a new line for better readability
    if (selectedNames.length > 1) {
      return selectedNames.join('\n');
    }

    return selectedNames[0];
  };

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={[]}
      render={({ field: { onChange, value }, fieldState: { error: fieldError } }) => {
        const selectedStudents = value || [];

        const toggleStudent = (studentId: string) => {
          const newSelection = selectedStudents.includes(studentId)
            ? selectedStudents.filter((id: string) => id !== studentId)
            : [...selectedStudents, studentId];
          onChange(newSelection);
        };

        return (
          <View style={styles.container}>
            <Text style={styles.label}>
              Split with Other users (Optional - Equal split only)
            </Text>

            <TouchableOpacity
              style={[
                styles.picker,
                selectedStudents.length > 0 && styles.pickerWithContent,
                (error || fieldError) && styles.pickerError,
                isOpen && styles.pickerOpen,
              ]}
              onPress={() => setIsOpen(!isOpen)}
            >
              <Text style={[
                styles.pickerText,
                selectedStudents.length === 0 && styles.placeholder,
                selectedStudents.length > 0 && styles.selectedText,
              ]}>
                {getDisplayText(selectedStudents)}
              </Text>
              <Text style={[styles.arrow, isOpen && styles.arrowOpen]}>▼</Text>
            </TouchableOpacity>

            {isOpen && (
              <View style={styles.dropdown}>
                <ScrollView
                  style={styles.dropdownList}
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled={true}
                >
                  {students.map(student =>
                    renderStudentItem(student, selectedStudents, toggleStudent)
                  )}
                </ScrollView>
              </View>
            )}

            {(error || fieldError) && (
              <Text style={styles.errorText}>{error || fieldError?.message}</Text>
            )}
          </View>
        );
      }}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    zIndex: 999,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 8,
  },
  picker: {
    borderWidth: 1,
    borderColor: Colors.gray300,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.background,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    minHeight: 48,
  },
  pickerWithContent: {
    alignItems: 'flex-start',
    paddingVertical: 16,
  },
  pickerError: {
    borderColor: Colors.error,
  },
  pickerOpen: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderBottomColor: Colors.gray200,
  },
  pickerText: {
    fontSize: 16,
    color: Colors.textPrimary,
    flex: 1,
    lineHeight: 22,
  },
  selectedText: {
    lineHeight: 22,
    flexWrap: 'wrap',
  },
  placeholder: {
    color: Colors.gray400,
  },
  arrow: {
    fontSize: 12,
    color: Colors.gray400,
    marginLeft: 8,
    marginTop: 2,
  },
  arrowOpen: {
    transform: [{ rotate: '180deg' }],
  },
  dropdown: {
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: Colors.gray300,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    backgroundColor: Colors.background,
    maxHeight: 200,
    position: 'relative',
    zIndex: 1000,
  },
  dropdownList: {
    maxHeight: 200,
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray100,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: Colors.gray300,
    borderRadius: 4,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  checkmark: {
    color: Colors.background,
    fontSize: 12,
    fontWeight: 'bold',
  },
  studentInfo: {
    flex: 1,
  },
  studentName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  studentEmail: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    marginTop: 4,
  },
});
