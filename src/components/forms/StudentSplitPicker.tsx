import React, { useState, useMemo, useEffect, useId } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, TextInput } from 'react-native';
import { Controller } from 'react-hook-form';
import { Colors } from '../../constants/colors';
import { mockUsers } from '../../store/mockData/users';
import { User, UserRole } from '../../types/User';
import { useDropdownContext } from '../../contexts/DropdownContext';

interface StudentSplitPickerProps {
  control: any;
  name: string;
  error?: string;
}

export default function StudentSplitPicker({
  control,
  name,
  error,
}: StudentSplitPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const dropdownManager = useDropdownContext();
  const dropdownId = useId();

  // Filter to get only students
  const students = mockUsers.filter(user => user.role === UserRole.STUDENT);

  // Filter students based on search query
  const filteredStudents = useMemo(() => {
    if (!searchQuery.trim()) {
      return students;
    }

    const query = searchQuery.toLowerCase().trim();
    return students.filter(student =>
      student.name.toLowerCase().includes(query) ||
      student.email.toLowerCase().includes(query)
    );
  }, [students, searchQuery]);

  useEffect(() => {
    dropdownManager.registerDropdown(dropdownId, () => setIsOpen(false));
    return () => dropdownManager.unregisterDropdown(dropdownId);
  }, [dropdownManager, dropdownId]);

  const handleToggle = () => {
    if (!isOpen) {
      dropdownManager.closeAllExcept(dropdownId);
      setSearchQuery(''); // Reset search when opening
    }
    setIsOpen(!isOpen);
  };

  const renderStudentItem = (
    student: User, 
    selectedStudents: string[], 
    onToggleStudent: (studentId: string) => void
  ) => {
    const isSelected = selectedStudents.includes(student.id);
    
    return (
      <TouchableOpacity
        key={student.id}
        style={styles.dropdownItem}
        onPress={() => onToggleStudent(student.id)}
      >
        <View style={styles.checkboxContainer}>
          <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
            {isSelected && <Text style={styles.checkmark}>✓</Text>}
          </View>
          <View style={styles.studentInfo}>
            <Text style={styles.studentName}>{student.name}</Text>
            <Text style={styles.studentEmail}>{student.email}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const getDisplayText = (selectedStudents: string[]) => {
    if (selectedStudents.length === 0) {
      return 'Select students to split with';
    }

    const selectedNames = selectedStudents
      .map(id => students.find(s => s.id === id)?.name)
      .filter(Boolean);

    if (selectedNames.length === 0) {
      return 'Selected students';
    }

    // For multiple students, show each on a new line for better readability
    if (selectedNames.length > 1) {
      return selectedNames.join('\n');
    }

    return selectedNames[0];
  };

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={[]}
      render={({ field: { onChange, value }, fieldState: { error: fieldError } }) => {
        const selectedStudents = value || [];

        const toggleStudent = (studentId: string) => {
          const newSelection = selectedStudents.includes(studentId)
            ? selectedStudents.filter((id: string) => id !== studentId)
            : [...selectedStudents, studentId];
          onChange(newSelection);
        };

        return (
          <View style={styles.container}>
            <Text style={styles.label}>
              Split with Other users (Optional - Equal split only)
            </Text>

            <TouchableOpacity
              style={[
                styles.picker,
                selectedStudents.length > 0 && styles.pickerWithContent,
                (error || fieldError) && styles.pickerError,
                isOpen && styles.pickerOpen,
              ]}
              onPress={handleToggle}
            >
              <Text style={[
                styles.pickerText,
                selectedStudents.length === 0 && styles.placeholder,
                selectedStudents.length > 0 && styles.selectedText,
              ]}>
                {getDisplayText(selectedStudents)}
              </Text>
              <Text style={[styles.arrow, isOpen && styles.arrowOpen]}>▼</Text>
            </TouchableOpacity>

            {isOpen && (
              <View style={styles.dropdown}>
                <View style={styles.searchContainer}>
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Search students..."
                    placeholderTextColor={Colors.gray400}
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                </View>
                <ScrollView
                  style={styles.dropdownList}
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled={true}
                >
                  {filteredStudents.length > 0 ? (
                    filteredStudents.map(student =>
                      renderStudentItem(student, selectedStudents, toggleStudent)
                    )
                  ) : (
                    <View style={styles.noResultsContainer}>
                      <Text style={styles.noResultsText}>
                        No students found matching "{searchQuery}"
                      </Text>
                    </View>
                  )}
                </ScrollView>
              </View>
            )}

            {(error || fieldError) && (
              <Text style={styles.errorText}>{error || fieldError?.message}</Text>
            )}
          </View>
        );
      }}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    zIndex: 999,
    position: 'relative',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 8,
  },
  picker: {
    borderWidth: 1,
    borderColor: Colors.gray300,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.background,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    minHeight: 48,
  },
  pickerWithContent: {
    alignItems: 'flex-start',
    paddingVertical: 16,
  },
  pickerError: {
    borderColor: Colors.error,
  },
  pickerOpen: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderBottomColor: Colors.gray200,
  },
  pickerText: {
    fontSize: 16,
    color: Colors.textPrimary,
    flex: 1,
    lineHeight: 22,
  },
  selectedText: {
    lineHeight: 22,
    flexWrap: 'wrap',
  },
  placeholder: {
    color: Colors.gray400,
  },
  arrow: {
    fontSize: 12,
    color: Colors.gray400,
    marginLeft: 8,
    marginTop: 2,
  },
  arrowOpen: {
    transform: [{ rotate: '180deg' }],
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: Colors.gray300,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    backgroundColor: Colors.background,
    maxHeight: 250,
    zIndex: 1000,
    elevation: 5, // For Android shadow
    shadowColor: '#000', // For iOS shadow
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  searchContainer: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: Colors.gray300,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    color: Colors.textPrimary,
    backgroundColor: Colors.background,
  },
  dropdownList: {
    maxHeight: 180, // Reduced to account for search input
  },
  noResultsContainer: {
    padding: 20,
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: 16,
    color: Colors.gray400,
    textAlign: 'center',
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray100,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: Colors.gray300,
    borderRadius: 4,
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  checkmark: {
    color: Colors.background,
    fontSize: 12,
    fontWeight: 'bold',
  },
  studentInfo: {
    flex: 1,
  },
  studentName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  studentEmail: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    marginTop: 4,
  },
});
