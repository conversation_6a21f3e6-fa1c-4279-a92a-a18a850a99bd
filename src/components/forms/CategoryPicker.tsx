import React, { useState, useEffect, useId } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { Controller } from 'react-hook-form';
import { Colors } from '../../constants/colors';
import { mockGLCodes, mockProgramCodes } from '../../store/mockData/categories';
import { GLCode, ProgramCode } from '../../types/Category';
import { useDropdownContext } from '../../contexts/DropdownContext';

interface CategoryPickerProps {
  control: any;
  name: string;
  label: string;
  type: 'gl' | 'program';
  error?: string;
}

export default function CategoryPicker({
  control,
  name,
  label,
  type,
  error,
}: CategoryPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownManager = useDropdownContext();
  const dropdownId = useId();
  const data = type === 'gl' ? mockGLCodes : mockProgramCodes;

  useEffect(() => {
    dropdownManager.registerDropdown(dropdownId, () => setIsOpen(false));
    return () => dropdownManager.unregisterDropdown(dropdownId);
  }, [dropdownManager, dropdownId]);

  const handleToggle = () => {
    if (!isOpen) {
      dropdownManager.closeAllExcept(dropdownId);
    }
    setIsOpen(!isOpen);
  };

  const renderItem = (item: GLCode | ProgramCode, onSelect: (value: string) => void) => (
    <TouchableOpacity
      key={item.id}
      style={styles.dropdownItem}
      onPress={() => {
        onSelect(item.code);
        setIsOpen(false);
      }}
    >
      <Text style={styles.itemCode}>{item.code}</Text>
      <Text style={styles.itemDescription}>{item.description}</Text>
    </TouchableOpacity>
  );

  return (
    <Controller
      control={control}
      name={name}
      rules={{ required: `${label} is required` }}
      render={({ field: { onChange, value }, fieldState: { error: fieldError } }) => {
        const selectedItem = data.find(item => item.code === value);

        return (
          <View style={styles.container}>
            <Text style={styles.label}>
              {label} <Text style={styles.required}>*</Text>
            </Text>

            <TouchableOpacity
              style={[
                styles.picker,
                (error || fieldError) && styles.pickerError,
                isOpen && styles.pickerOpen,
              ]}
              onPress={handleToggle}
            >
              <Text style={[
                styles.pickerText,
                !selectedItem && styles.placeholder,
              ]}>
                {selectedItem ? `${selectedItem.code} - ${selectedItem.description}` : `Select ${label}`}
              </Text>
              <Text style={[styles.arrow, isOpen && styles.arrowOpen]}>▼</Text>
            </TouchableOpacity>

            {isOpen && (
              <View style={styles.dropdown}>
                <ScrollView
                  style={styles.dropdownList}
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled={true}
                >
                  {data.filter(item => item.isActive).map(item =>
                    renderItem(item, onChange)
                  )}
                </ScrollView>
              </View>
            )}

            {(error || fieldError) && (
              <Text style={styles.errorText}>{error || fieldError?.message}</Text>
            )}
          </View>
        );
      }}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    // zIndex: 1000,
    position: 'relative',
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
    marginBottom: 8,
  },
  required: {
    color: Colors.error,
  },
  picker: {
    borderWidth: 1,
    borderColor: Colors.gray300,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.background,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pickerError: {
    borderColor: Colors.error,
  },
  pickerOpen: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderBottomColor: Colors.gray200,
  },
  pickerText: {
    fontSize: 16,
    color: Colors.textPrimary,
    flex: 1,
  },
  placeholder: {
    color: Colors.gray400,
  },
  arrow: {
    fontSize: 12,
    color: Colors.gray400,
    marginLeft: 8,
  },
  arrowOpen: {
    transform: [{ rotate: '180deg' }],
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: Colors.gray300,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    backgroundColor: Colors.background,
    maxHeight: 200,
    zIndex: 1001,
    elevation: 5, // For Android shadow
    shadowColor: '#000', // For iOS shadow
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  dropdownList: {
    maxHeight: 200,
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray100,
  },
  itemCode: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  itemDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    marginTop: 4,
  },
});
